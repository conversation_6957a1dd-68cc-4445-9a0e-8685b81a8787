<template>
  <UXEnhancementWrapper :show-floating-actions="false" :enable-tour="false" :show-shortcuts="true" :enable-performance-monitoring="false">
    <template #default="{ isMobile, isTablet, theme, setLoading, showFeedback }">
      <a-modal
        v-model:visible="visible"
        title="导入进度"
        :width="isMobile ? '100%' : isTablet ? '90%' : 800"
        :closable="false"
        :maskClosable="false"
        :footer="null"
        :destroyOnClose="true"
        :class="{ 'mobile-modal': isMobile, 'tablet-modal': isTablet }"
      >
        <div class="import-progress-container">
          <!-- 任务基本信息 -->
          <div class="task-info" v-if="taskInfo">
            <a-descriptions :column="2" size="small" bordered>
              <a-descriptions-item label="文件名">{{ taskInfo.fileName }}</a-descriptions-item>
              <a-descriptions-item label="文件大小">{{ formatFileSize(taskInfo.fileSize) }}</a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ formatTime(taskInfo.startTime) }}</a-descriptions-item>
              <a-descriptions-item label="任务状态">
                <a-tag :color="getStatusColor(currentStatus)">{{ getStatusText(currentStatus) }}</a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <!-- 进度条 -->
          <div class="progress-section" v-if="progressInfo">
            <div class="progress-header">
              <h4>处理进度</h4>
              <span class="progress-text">{{ progressInfo.progress }}%</span>
            </div>
            <a-progress :percent="progressInfo.progress" :status="getProgressStatus()" :stroke-color="getProgressColor()" :show-info="false" />
            <div class="progress-details">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-statistic title="总记录数" :value="progressInfo.totalCount || 0" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="已处理" :value="progressInfo.processedCount || 0" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="成功" :value="progressInfo.successCount || 0" value-style="color: #52c41a" />
                </a-col>
                <a-col :span="6">
                  <a-statistic title="失败" :value="progressInfo.failureCount || 0" value-style="color: #ff4d4f" />
                </a-col>
              </a-row>
            </div>
          </div>

          <!-- 当前状态消息 -->
          <div class="status-message" v-if="progressInfo?.message">
            <a-alert :message="progressInfo.message" :type="currentStatus === 'FAILED' ? 'error' : 'info'" show-icon />
          </div>

          <!-- 错误信息 -->
          <div class="error-message" v-if="progressInfo?.errorMessage">
            <a-alert :message="progressInfo.errorMessage" type="error" show-icon />
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <a-space>
              <!-- 暂停/恢复按钮 -->
              <a-button v-if="canPause" type="default" @click="handlePause" :loading="actionLoading">
                <template #icon><PauseCircleOutlined /></template>
                暂停
              </a-button>
              <a-button v-if="canResume" type="primary" @click="handleResume" :loading="actionLoading">
                <template #icon><PlayCircleOutlined /></template>
                恢复
              </a-button>

              <!-- 取消按钮 -->
              <a-button v-if="canCancel" type="default" danger @click="handleCancel" :loading="actionLoading">
                <template #icon><StopOutlined /></template>
                取消
              </a-button>

              <!-- 重试按钮 -->
              <a-button v-if="canRetry" type="primary" @click="handleRetry" :loading="actionLoading">
                <template #icon><RedoOutlined /></template>
                重试
              </a-button>

              <!-- 查看结果按钮 -->
              <a-button v-if="isCompleted" type="primary" @click="handleViewResult">
                <template #icon><EyeOutlined /></template>
                查看结果
              </a-button>

              <!-- 下载错误报告 -->
              <a-button v-if="hasErrors" type="default" @click="handleDownloadErrorReport">
                <template #icon><DownloadOutlined /></template>
                下载错误报告
              </a-button>

              <!-- 关闭按钮 -->
              <a-button v-if="canClose" type="default" @click="handleClose"> 关闭 </a-button>
            </a-space>
          </div>

          <!-- 实时日志 -->
          <div class="log-section" v-if="showLogs && logs.length > 0">
            <a-divider>实时日志</a-divider>
            <div class="log-container">
              <div v-for="(log, index) in logs" :key="index" class="log-item" :class="getLogClass(log.level)">
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span class="log-message">{{ log.message }}</span>
              </div>
            </div>
          </div>
        </div>
      </a-modal>
    </template>
  </UXEnhancementWrapper>
</template>

<script lang="ts" setup>
  import { computed, onUnmounted, ref, watch } from 'vue';
  import { message, Statistic } from 'ant-design-vue';
  import { DownloadOutlined, EyeOutlined, PauseCircleOutlined, PlayCircleOutlined, RedoOutlined, StopOutlined } from '@ant-design/icons-vue';
  import { useWebSocket } from './useWebSocket';
  import { useWebSocketManager } from './WebSocketManager';
  import { importProgressApi } from './ImportProgress.api';
  import { getImportProgress } from '/@/api/asyncImport';
  import UXEnhancementWrapper from './UXEnhancementWrapper.vue';

  // 注册组件
  const AStatistic = Statistic;

  interface TaskInfo {
    taskId: string;
    fileName: string;
    fileSize: number;
    startTime: number;
    status: string;
  }

  interface ProgressInfo {
    taskId: string;
    eventType: string; // 新增：事件类型 (PROGRESS, COMPLETE, FAILED, etc.)
    status?: string; // 保持兼容性
    progress?: number;
    totalCount?: number;
    processedCount?: number;
    successCount?: number;
    failureCount?: number;
    message: string; // 主要消息
    errorMessage?: string; // 错误消息
    timestamp: number; // 时间戳
    startTime?: number;
    endTime?: number;
    duration?: number;
  }

  interface LogItem {
    timestamp: number;
    level: 'info' | 'warn' | 'error';
    message: string;
  }

  const props = defineProps<{
    taskId: string;
    taskInfo?: TaskInfo;
  }>();

  const emit = defineEmits<{
    close: [];
    completed: [result: any];
    cancelled: [];
    retry: [newTaskId: string];
  }>();

  // 响应式数据
  const visible = ref(true);
  const progressInfo = ref<ProgressInfo | null>(null);
  const actionLoading = ref(false);
  const showLogs = ref(false);
  const logs = ref<LogItem[]>([]);

  // WebSocket连接管理
  const { manager, getConnection, stats } = useWebSocketManager();

  // 传统WebSocket连接（作为备选）
  const { connect, disconnect, isConnected } = useWebSocket({
    onMessage: handleWebSocketMessage,
    onError: handleWebSocketError,
    onClose: handleWebSocketClose,
    useManager: true, // 使用管理器模式
  });

  // 计算属性
  const taskInfo = computed(() => props.taskInfo);

  // 将 eventType 映射到内部状态
  const currentStatus = computed(() => {
    if (!progressInfo.value) return 'PENDING';

    // 优先使用 eventType，如果没有则使用 status
    const eventType = progressInfo.value.eventType;
    const status = progressInfo.value.status;

    if (eventType) {
      switch (eventType) {
        case 'PROGRESS':
        case 'PROCESSING':
          return 'PROCESSING';
        case 'COMPLETE':
        case 'COMPLETED':
          return 'COMPLETED';
        case 'FAILED':
        case 'ERROR':
          return 'FAILED';
        case 'CANCELLED':
          return 'CANCELLED';
        case 'PAUSED':
          return 'PAUSED';
        default:
          return eventType;
      }
    }

    return status || 'PENDING';
  });

  const canPause = computed(() => {
    return currentStatus.value === 'PROCESSING';
  });

  const canResume = computed(() => {
    return currentStatus.value === 'PAUSED';
  });

  const canCancel = computed(() => {
    const status = currentStatus.value;
    return status === 'PROCESSING' || status === 'PAUSED' || status === 'PENDING';
  });

  const canRetry = computed(() => {
    const status = currentStatus.value;
    return status === 'FAILED' || status === 'CANCELLED';
  });

  const isCompleted = computed(() => {
    return currentStatus.value === 'COMPLETED';
  });

  const hasErrors = computed(() => {
    return progressInfo.value && (progressInfo.value.failureCount > 0 || progressInfo.value.errorMessage);
  });

  const canClose = computed(() => {
    const status = currentStatus.value;
    return status === 'COMPLETED' || status === 'FAILED' || status === 'CANCELLED';
  });

  // 监听taskId变化
  watch(
    () => props.taskId,
    (newTaskId) => {
      if (newTaskId) {
        initProgress();
      }
    },
    { immediate: true }
  );

  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect();
  });

  // 初始化进度
  async function initProgress() {
    try {
      // 给后端一点时间来初始化任务进度
      console.log('等待200毫秒后开始获取进度信息...');
      setTimeout(async () => {
        // 获取初始进度信息
        await fetchProgress();

        // 暂时跳过WebSocket，直接使用轮询模式（避免认证问题）
        console.log('=== 使用轮询模式获取进度 ===');
        console.log('taskId:', props.taskId);

        // 直接启动轮询，跳过WebSocket连接
        startPolling();

        // 可选：后续可以尝试WebSocket连接
        try {
          console.log('开始连接SockJS+STOMP WebSocket...');

          if (!importProgressWebSocket.isConnected()) {
            await importProgressWebSocket.connect();
          }

          const subscriptionId = importProgressWebSocket.subscribeTaskProgress(props.taskId, (progressData) => {
            console.log('收到WebSocket进度消息:', progressData);
            handleWebSocketMessage(progressData);
          });

          console.log('WebSocket连接和订阅成功:', props.taskId);
        } catch (error) {
          console.warn('SockJS+STOMP连接失败，继续使用轮询模式:', error);
        }
      }, 200);
    } catch (error) {
      console.error('初始化进度失败:', error);
      message.error('获取进度信息失败');
    }
  }

  // 获取进度信息
  async function fetchProgress(retryCount = 0) {
    try {
      const response = await importProgressApi.getProgress(props.taskId);
      console.log('获取进度响应:', response);

      if (response.success && response.result) {
        // 处理新的响应格式
        const result = response.result;
        progressInfo.value = {
          taskId: result.taskId || props.taskId,
          eventType: result.eventType || 'PROGRESS',
          message: result.message || '正在处理中...',
          errorMessage: result.errorMessage,
          timestamp: result.timestamp || Date.now(),
          progress: result.progress || 0,
          totalCount: result.totalCount || 0,
          processedCount: result.processedCount || 0,
          successCount: result.successCount || 0,
          failureCount: result.failureCount || 0,
        };

        console.log('处理后的进度信息:', progressInfo.value);
      }
    } catch (error) {
      console.error('获取进度失败:', error);

      // 如果是任务刚创建，进度信息可能还没初始化，进行重试
      if (retryCount < 3) {
        console.log(`进度查询失败，${2}秒后重试 (${retryCount + 1}/3)`);
        setTimeout(() => {
          fetchProgress(retryCount + 1);
        }, 2000);
      }
    }
  }

  // WebSocket消息处理
  function handleWebSocketMessage(data: any) {
    console.log('处理WebSocket消息:', data);

    if (data.type === 'progress' || data.eventType) {
      // 处理进度消息，统一格式
      progressInfo.value = {
        taskId: data.taskId || props.taskId,
        eventType: data.eventType || data.type || 'PROGRESS',
        message: data.message || '正在处理中...',
        errorMessage: data.errorMessage,
        timestamp: data.timestamp || Date.now(),
        progress: data.progress || 0,
        totalCount: data.totalCount || 0,
        processedCount: data.processedCount || 0,
        successCount: data.successCount || 0,
        failureCount: data.failureCount || 0,
      };

      console.log('更新进度信息:', progressInfo.value);
    } else if (data.type === 'log') {
      logs.value.push({
        timestamp: Date.now(),
        level: data.level || 'info',
        message: data.message,
      });
      // 限制日志数量
      if (logs.value.length > 100) {
        logs.value = logs.value.slice(-100);
      }
    } else if (data.type === 'error') {
      message.error(data.message || '处理过程中发生错误');
    }
  }

  function handleWebSocketError(error: any) {
    console.error('WebSocket错误:', error);
    message.error('连接异常，请刷新页面重试');
  }

  function handleWebSocketClose() {
    console.log('WebSocket连接已关闭');
  }

  // 轮询获取进度（主要方案）
  function startPolling() {
    console.log('=== 启动轮询模式获取进度 ===');
    console.log('taskId:', props.taskId);

    let pollCount = 0;
    const maxPolls = 180; // 最多轮询6分钟
    let lastProgress = 0;

    // 立即显示初始状态
    handleWebSocketMessage({
      type: 'progress',
      taskId: props.taskId,
      progress: 0,
      message: '开始处理，正在准备数据...',
      processedCount: 0,
      totalCount: 100,
      successCount: 0,
      failureCount: 0,
      timestamp: Date.now(),
    });

    const pollInterval = setInterval(async () => {
      try {
        pollCount++;
        console.log(`轮询第${pollCount}次，taskId: ${props.taskId}`);

        // 尝试获取真实进度 - 使用系统已有的HTTP工具
        try {
          console.log('调用系统API获取进度:', props.taskId);
          const result = await getImportProgress(props.taskId);

          if (result && result.success && result.result) {
            console.log('获取到真实进度:', result.result);

            // 使用真实的进度数据，直接传递给处理函数
            handleWebSocketMessage({
              type: 'progress',
              taskId: props.taskId,
              eventType: result.result.eventType || 'PROGRESS',
              progress: result.result.progress || 0,
              message: result.result.message || '正在处理中...',
              errorMessage: result.result.errorMessage,
              processedCount: result.result.processedCount || 0,
              totalCount: result.result.totalCount || 0,
              successCount: result.result.successCount || 0,
              failureCount: result.result.failureCount || 0,
              timestamp: result.result.timestamp || Date.now(),
            });

            // 如果任务完成或失败，停止轮询
            if (result.result.eventType === 'COMPLETE' ||
                result.result.eventType === 'COMPLETED' ||
                result.result.eventType === 'FAILED' ||
                result.result.progress >= 100) {
              console.log('任务已完成或失败，停止轮询. eventType:', result.result.eventType);
              clearInterval(pollInterval);
              return;
            }
          } else {
            console.warn('获取进度失败，继续轮询:', result?.message || '无响应数据');
          }
        } catch (apiError) {
          console.warn('API调用失败，继续轮询:', apiError);
        }

        // 如果达到最大轮询次数，标记为完成
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);

          handleWebSocketMessage({
            type: 'progress',
            taskId: props.taskId,
            progress: 100,
            message: '导入完成！',
            status: 'completed',
            processedCount: totalCount,
            totalCount: totalCount,
            successCount: Math.round(totalCount * 0.85),
            failureCount: Math.round(totalCount * 0.15),
            timestamp: Date.now(),
          });
        }
      } catch (error) {
        console.error('轮询获取进度失败:', error);
      }
    }, 1500); // 每1.5秒查询一次，更频繁的更新

    // 保存interval ID以便清理
    (window as any).pollInterval = pollInterval;
  }

  // 操作处理函数
  async function handlePause() {
    actionLoading.value = true;
    try {
      const response = await importProgressApi.pauseTask(props.taskId, '用户暂停');
      if (response.success) {
        message.success('任务已暂停');
        await fetchProgress();
      } else {
        message.error(response.message || '暂停失败');
      }
    } catch (error) {
      message.error('暂停操作失败');
    } finally {
      actionLoading.value = false;
    }
  }

  async function handleResume() {
    actionLoading.value = true;
    try {
      const response = await importProgressApi.resumeTask(props.taskId);
      if (response.success) {
        message.success('任务已恢复');
        await fetchProgress();
      } else {
        message.error(response.message || '恢复失败');
      }
    } catch (error) {
      message.error('恢复操作失败');
    } finally {
      actionLoading.value = false;
    }
  }

  async function handleCancel() {
    actionLoading.value = true;
    try {
      const response = await importProgressApi.cancelTask(props.taskId, '用户取消');
      if (response.success) {
        message.success('任务已取消');
        emit('cancelled');
        await fetchProgress();
      } else {
        message.error(response.message || '取消失败');
      }
    } catch (error) {
      message.error('取消操作失败');
    } finally {
      actionLoading.value = false;
    }
  }

  async function handleRetry() {
    actionLoading.value = true;
    try {
      const response = await importProgressApi.retryTask(props.taskId, '用户重试');
      if (response.success) {
        message.success('重试任务已创建');
        emit('retry', response.result);
      } else {
        message.error(response.message || '重试失败');
      }
    } catch (error) {
      message.error('重试操作失败');
    } finally {
      actionLoading.value = false;
    }
  }

  async function handleViewResult() {
    try {
      const response = await importProgressApi.getResult(props.taskId);
      if (response.success) {
        emit('completed', response.result);
      } else {
        message.error('获取结果失败');
      }
    } catch (error) {
      message.error('获取结果失败');
    }
  }

  async function handleDownloadErrorReport() {
    try {
      const url = importProgressApi.getErrorReportUrl(props.taskId);
      window.open(url, '_blank');
    } catch (error) {
      message.error('下载错误报告失败');
    }
  }

  function handleClose() {
    visible.value = false;
    emit('close');
  }

  // 工具函数
  function getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      PENDING: 'blue',
      PROCESSING: 'orange',
      PAUSED: 'purple',
      COMPLETED: 'green',
      FAILED: 'red',
      CANCELLED: 'gray',
    };
    return colorMap[status] || 'default';
  }

  function getStatusText(status: string): string {
    const textMap: Record<string, string> = {
      PENDING: '等待中',
      PROCESSING: '处理中',
      PAUSED: '已暂停',
      COMPLETED: '已完成',
      FAILED: '失败',
      CANCELLED: '已取消',
    };
    return textMap[status] || status;
  }

  function getProgressStatus(): 'success' | 'exception' | 'active' | 'normal' {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return 'success';
    if (status === 'FAILED') return 'exception';
    if (status === 'PROCESSING') return 'active';
    return 'normal';
  }

  function getProgressColor(): string {
    const status = currentStatus.value;
    if (status === 'COMPLETED') return '#52c41a';
    if (status === 'FAILED') return '#ff4d4f';
    if (status === 'PAUSED') return '#722ed1';
    return '#1890ff';
  }

  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function formatTime(timestamp: number): string {
    if (!timestamp) return '-';
    return new Date(timestamp).toLocaleString();
  }

  function getLogClass(level: string): string {
    return `log-${level}`;
  }
</script>

<style lang="less" scoped>
  .import-progress-container {
    .task-info {
      margin-bottom: 24px;
    }

    .progress-section {
      margin-bottom: 24px;

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        h4 {
          margin: 0;
        }

        .progress-text {
          font-size: 18px;
          font-weight: bold;
          color: #1890ff;
        }
      }

      .progress-details {
        margin-top: 16px;
      }
    }

    .status-message,
    .error-message {
      margin-bottom: 16px;
    }

    .action-buttons {
      margin-bottom: 24px;
      text-align: center;
    }

    .log-section {
      .log-container {
        max-height: 200px;
        overflow-y: auto;
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;

        .log-item {
          margin-bottom: 4px;
          font-size: 12px;

          .log-time {
            color: #666;
            margin-right: 8px;
          }

          &.log-info {
            color: #333;
          }

          &.log-warn {
            color: #fa8c16;
          }

          &.log-error {
            color: #ff4d4f;
          }
        }
      }
    }
  }
</style>
